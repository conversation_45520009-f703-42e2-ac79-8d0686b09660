/**
 * MovingBGSprite
 * 组件类 - 从编译后的JS反编译生成
 */

const $2Notifier = require('Notifier');
const $2ListenID = require('ListenID');
const $2MovingBGAssembler = require('MovingBGAssembler');

var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var f = false;

exports.default = cc.Class({
    extends: cc.Sprite,

    properties: {
        _moveSpeed: {
            type: cc.Vec2,
            default: null
        },
        moveSpeed: {
            get() {
                return this._moveSpeed;
            },
            set(value) {
                f && (e = cc.Vec2.ZERO);
                this._moveSpeed = e;
                this.FlushProperties();
            },
            visible: false
        }
    },

    ctor: function () {
        this._moveSpeed = cc.Vec2.ZERO
    },

    // use this for initialization
    onLoad: function () {
    },

    onDestroy: function () {
        this._super();
        $2Notifier.Notifier.changeListener(false, $2ListenID.ListenID.NO_BG_Moving, this.noMoving, this);
    },

    noMoving: function () {
        f = true;
        this.moveSpeed = cc.Vec2.ZERO;
    },

    FlushProperties: function () {
        var e = this._assembler;
        if (e) {
        e.moveSpeed = f ? cc.Vec2.ZERO : this._moveSpeed;
        this.setVertsDirty();
        }
    },

    onEnable: function () {
        this._super();
    },

    _resetAssembler: function () {
        this.setVertsDirty();
        var e = this._assembler = new $2MovingBGAssembler.default();
        this.FlushProperties();
        e.init(this);
        this._updateColor();
    },

    setMoveSpeed: function (e) {
        var t;
        this.moveSpeed.set(e);
        null === (t = this._assembler) || undefined === t || t.moveSpeed.set(e);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
