/**
 * FCollider
 * 组件类 - 从编译后的JS反编译生成
 */

const $2BaseEntity = require('BaseEntity');
const $2FColliderManager = require('FColliderManager');

var a;
(function (e) {
    e[e.Circle = 0] = "Circle";
    e[e.Box = 1] = "Box";
    e[e.Polygon = 2] = "Polygon";
})(a = exports.ColliderType || (exports.ColliderType = {}));
(function (e) {
    e[e.Circle = 0] = "Circle";
    e[e.Box = 1] = "Box";
    e[e.Polygon = 2] = "Polygon";
    e[e.IsTest = 1] = "IsTest";
    e[e.NoTest = 2] = "NoTest";
})(exports.StateType || (exports.StateType = {}));
var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;

exports.default = cc.Class({
    extends: cc.Component,

    properties: {
        listenCom: {
            type: cc.Node,
            default: null
        },
        _offset: {

            type: cc.Vec2,

            default: function() { return new cc.Vec2(); }

        },
        type: {
            get() {
                return a.Box;
            },
            visible: false
        },
        position: {
            get() {
                return this.node.position;
            },
            visible: false
        },
        comp: {
            get() {
                return this._comp;
            },
            visible: false
        },
        x: {
            get() {
                return this.aabb.x;
            },
            visible: false
        },
        y: {
            get() {
                return this.aabb.y;
            },
            visible: false
        },
        width: {
            get() {
                return this.aabb.width;
            },
            visible: false
        },
        height: {
            get() {
                return this.aabb.height;
            },
            visible: false
        },
        offset: {
            get() {
                return this._offset;
            },
            set(value) {
                this._offset = e;
            },
            visible: false
        }
    },

    ctor: function () {
        this.listenCom = null
        this.isConvex = true
        this.aabb = cc.rect()
        this.cindex = []
        this._comp = null
        this.isDirty = true
        this.isActive = true
        this.colliderId = 0
        this.contactMap = new Map()
        this._offset = cc.v2(0, 0)
    },

    // use this for initialization
    onLoad: function () {
    },

    setSize: function () {
        // TODO: 实现方法逻辑
    },

    setComp: function (e) {
        this._comp = e;
    },

    setActive: function (e, t) {
        undefined === e && (e = true);
        undefined === t && (t = 0);
        if (this.isActive == e) {
        return this;
        } else {
        return t ? this.scheduleOnce(this.setActive, t) : this.isActive = e, this;
        }
    },

    initCollider: function () {
        this.colliderId = o._baseId++;
        o._baseId > 5e10 && (o._baseId = 1);
    },

    onEnable: function () {
        var e;
        this.addCollider();
        if (this.listenCom) {
        var t = this.listenCom.getComponent($2BaseEntity.default);
        this.setComp(t);
        t.collider = this;
        } else {
        this.setComp(this.getComponent($2BaseEntity.default) || (null === (e = this.listenCom) || undefined === e ? undefined : e.getComponent($2BaseEntity.default)));
        }
    },

    onDisable: function () {
        this.removeCollider();
        this.unscheduleAllCallbacks();
    },

    addCollider: function () {
        $2FColliderManager.default.instance.addCollider(this);
    },

    removeCollider: function () {
        this.isActive = false;
        $2FColliderManager.default.instance.removeCollider(this);
    },

    // called every frame, uncomment this function to activate update callback
    // update: function (dt) {
    // },
});
